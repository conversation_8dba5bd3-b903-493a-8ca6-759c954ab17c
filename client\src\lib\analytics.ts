// Core Web Vitals monitoring and analytics
export interface PerformanceData {
  cls: number;
  fid: number;
  fcp: number;
  lcp: number;
  ttfb: number;
  timestamp: number;
  url: string;
  userAgent: string;
}

// Web Vitals thresholds
const THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  FID: { good: 100, poor: 300 },
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 }
};

// Rate metric based on thresholds
function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const threshold = THRESHOLDS[name as keyof typeof THRESHOLDS];
  if (!threshold) return 'good';
  
  if (value <= threshold.good) return 'good';
  if (value <= threshold.poor) return 'needs-improvement';
  return 'poor';
}

// Store performance data locally
function storePerformanceData(data: PerformanceData) {
  try {
    const stored = localStorage.getItem('ideahunter_performance');
    const history = stored ? JSON.parse(stored) : [];
    
    // Keep only last 50 entries
    history.push(data);
    if (history.length > 50) {
      history.shift();
    }
    
    localStorage.setItem('ideahunter_performance', JSON.stringify(history));
  } catch (error) {
    console.warn('Failed to store performance data:', error);
  }
}

// Get performance history
export function getPerformanceHistory(): PerformanceData[] {
  try {
    const stored = localStorage.getItem('ideahunter_performance');
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.warn('Failed to get performance history:', error);
    return [];
  }
}

// Calculate performance score (0-100)
export function calculatePerformanceScore(data: PerformanceData): number {
  const scores = {
    cls: getRating('CLS', data.cls) === 'good' ? 100 : getRating('CLS', data.cls) === 'needs-improvement' ? 50 : 0,
    fid: getRating('FID', data.fid) === 'good' ? 100 : getRating('FID', data.fid) === 'needs-improvement' ? 50 : 0,
    fcp: getRating('FCP', data.fcp) === 'good' ? 100 : getRating('FCP', data.fcp) === 'needs-improvement' ? 50 : 0,
    lcp: getRating('LCP', data.lcp) === 'good' ? 100 : getRating('LCP', data.lcp) === 'needs-improvement' ? 50 : 0,
    ttfb: getRating('TTFB', data.ttfb) === 'good' ? 100 : getRating('TTFB', data.ttfb) === 'needs-improvement' ? 50 : 0
  };
  
  return Math.round((scores.cls + scores.fid + scores.fcp + scores.lcp + scores.ttfb) / 5);
}

// Track Web Vitals (simplified without external library)
export function trackWebVitals() {
  if (typeof window === 'undefined') return;

  const performanceData: Partial<PerformanceData> = {
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent
  };

  // Simple performance tracking using built-in APIs
  if ('performance' in window) {
    // Track page load time as a basic metric
    window.addEventListener('load', () => {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      performanceData.lcp = loadTime;
      console.log('Page Load Time:', loadTime);

      // Store performance data
      storePerformanceData(performanceData as PerformanceData);
    });
  }
}

// Initialize performance monitoring
export function initPerformanceMonitoring() {
  // Track Web Vitals
  trackWebVitals();
  
  // Track page load time
  window.addEventListener('load', () => {
    const loadTime = performance.now();
    console.log('Page load time:', loadTime);
    
    if (window.gtag) {
      window.gtag('event', 'page_load_time', {
        event_category: 'Performance',
        value: Math.round(loadTime)
      });
    }
  });

  // Track navigation timing
  if ('navigation' in performance) {
    const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navTiming) {
      const metrics = {
        dns: navTiming.domainLookupEnd - navTiming.domainLookupStart,
        tcp: navTiming.connectEnd - navTiming.connectStart,
        request: navTiming.responseStart - navTiming.requestStart,
        response: navTiming.responseEnd - navTiming.responseStart,
        dom: navTiming.domContentLoadedEventEnd - navTiming.domContentLoadedEventStart
      };
      
      console.log('Navigation timing:', metrics);
    }
  }
}

// Export for global access
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

export default {
  trackWebVitals,
  initPerformanceMonitoring,
  getPerformanceHistory,
  calculatePerformanceScore
};
